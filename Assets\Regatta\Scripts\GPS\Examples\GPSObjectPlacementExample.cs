using UnityEngine;
using Regatta.GPS;

namespace Regatta.GPS.Examples
{
    /// <summary>
    /// Exemple d'utilisation du système de placement d'objets GPS
    /// Ce script montre comment placer automatiquement des phares, clubs, et autres points d'intérêt
    /// </summary>
    public class GPSObjectPlacementExample : MonoBehaviour
    {
        [Header("Préfabs d'objets")]
        [SerializeField] private GameObject pharePrefab;
        [SerializeField] private GameObject clubPrefab;
        [SerializeField] private GameObject bouePrefab;
        [SerializeField] private GameObject batimentPrefab;

        [Header("Configuration")]
        [SerializeField] private bool createExampleObjects = true;
        [SerializeField] private Transform parentContainer;

        private GPSObjectPlacer gpsObjectPlacer;

        private void Start()
        {
            if (createExampleObjects)
            {
                CreateExampleObjects();
            }
        }

        /// <summary>
        /// Crée des objets d'exemple avec des coordonnées GPS réelles
        /// </summary>
        private void CreateExampleObjects()
        {
            // Trouver ou créer le GPSObjectPlacer
            gpsObjectPlacer = FindFirstObjectByType<GPSObjectPlacer>();
            if (gpsObjectPlacer == null)
            {
                GameObject placerObj = new GameObject("GPSObjectPlacer");
                gpsObjectPlacer = placerObj.AddComponent<GPSObjectPlacer>();
            }

            // Créer un conteneur pour organiser les objets
            if (parentContainer == null)
            {
                parentContainer = new GameObject("Objets GPS").transform;
            }

            // Exemples de phares en Bretagne (coordonnées réelles)
            CreateLighthouse("Phare du Créac'h", 48.4558, -5.1281, pharePrefab);
            CreateLighthouse("Phare de la Jument", 48.4225, -5.1336, pharePrefab);
            CreateLighthouse("Phare d'Ar-Men", 48.0306, -4.9997, pharePrefab);

            // Exemples de clubs nautiques
            CreateClub("Yacht Club de France", 48.8566, 2.3522, clubPrefab);
            CreateClub("Club Nautique de Brest", 48.3833, -4.5000, clubPrefab);

            // Exemples de bouées de navigation
            CreateNavigationBuoy("Bouée Cardinal Nord", 48.4000, -4.8000, bouePrefab);
            CreateNavigationBuoy("Bouée Cardinal Sud", 48.3500, -4.7500, bouePrefab);

            // Exemples de bâtiments remarquables
            CreateBuilding("Tour Eiffel", 48.8584, 2.2945, batimentPrefab);
            CreateBuilding("Château de Brest", 48.3833, -4.4950, batimentPrefab);

            Debug.Log("[GPSObjectPlacementExample] Objets d'exemple créés avec succès !");
        }

        /// <summary>
        /// Crée un phare à des coordonnées GPS spécifiques
        /// </summary>
        private void CreateLighthouse(string name, double latitude, double longitude, GameObject prefab)
        {
            if (prefab == null) return;

            GameObject lighthouse = Instantiate(prefab, parentContainer);
            lighthouse.name = name;

            // Ajouter le composant GPS
            var gpsComponent = lighthouse.AddComponent<GPSPlaceableComponent>();
            gpsComponent.latitude = latitude;
            gpsComponent.longitude = longitude;
            gpsComponent.customHeight = 5f; // Phares en hauteur
            gpsComponent.showLogs = true;

            // Ou utiliser le GPSObjectPlacer directement
            gpsObjectPlacer.AddGPSObject(lighthouse, latitude, longitude, name);
        }

        /// <summary>
        /// Crée un club nautique
        /// </summary>
        private void CreateClub(string name, double latitude, double longitude, GameObject prefab)
        {
            if (prefab == null) return;

            GameObject club = Instantiate(prefab, parentContainer);
            club.name = name;

            var gpsComponent = club.AddComponent<GPSPlaceableComponent>();
            gpsComponent.latitude = latitude;
            gpsComponent.longitude = longitude;
            gpsComponent.customHeight = 1f;
            gpsComponent.rotationY = Random.Range(0f, 360f); // Rotation aléatoire
        }

        /// <summary>
        /// Crée une bouée de navigation
        /// </summary>
        private void CreateNavigationBuoy(string name, double latitude, double longitude, GameObject prefab)
        {
            if (prefab == null) return;

            GameObject buoy = Instantiate(prefab, parentContainer);
            buoy.name = name;

            var gpsComponent = buoy.AddComponent<GPSPlaceableComponent>();
            gpsComponent.latitude = latitude;
            gpsComponent.longitude = longitude;
            gpsComponent.customHeight = 0.5f; // Bouées au niveau de l'eau
        }

        /// <summary>
        /// Crée un bâtiment remarquable
        /// </summary>
        private void CreateBuilding(string name, double latitude, double longitude, GameObject prefab)
        {
            if (prefab == null) return;

            GameObject building = Instantiate(prefab, parentContainer);
            building.name = name;

            var gpsComponent = building.AddComponent<GPSPlaceableComponent>();
            gpsComponent.latitude = latitude;
            gpsComponent.longitude = longitude;
            gpsComponent.customHeight = 2f;
        }

        /// <summary>
        /// Exemple de placement dynamique d'objets
        /// </summary>
        [ContextMenu("Ajouter un objet GPS dynamiquement")]
        public void AddDynamicGPSObject()
        {
            if (pharePrefab == null)
            {
                Debug.LogWarning("Aucun préfab de phare assigné !");
                return;
            }

            // Coordonnées d'exemple (Phare de Cordouan)
            double latitude = 45.5858;
            double longitude = -1.1711;

            GameObject newLighthouse = Instantiate(pharePrefab, parentContainer);
            newLighthouse.name = "Phare de Cordouan (Dynamique)";

            // Méthode 1: Utiliser le composant GPSPlaceableComponent
            var gpsComponent = newLighthouse.AddComponent<GPSPlaceableComponent>();
            gpsComponent.latitude = latitude;
            gpsComponent.longitude = longitude;
            gpsComponent.customHeight = 8f;
            gpsComponent.PlaceObject(); // Placement immédiat

            Debug.Log($"Objet GPS dynamique créé: {newLighthouse.name}");
        }

        /// <summary>
        /// Exemple de mise à jour des coordonnées d'un objet existant
        /// </summary>
        [ContextMenu("Déplacer un objet GPS")]
        public void MoveGPSObject()
        {
            var gpsComponents = FindObjectsByType<GPSPlaceableComponent>(FindObjectsSortMode.None);
            
            if (gpsComponents.Length == 0)
            {
                Debug.LogWarning("Aucun objet GPS trouvé !");
                return;
            }

            // Prendre le premier objet et le déplacer légèrement
            var firstObject = gpsComponents[0];
            double newLat = firstObject.latitude + 0.001; // Déplacement de ~100m
            double newLon = firstObject.longitude + 0.001;

            Debug.Log($"Déplacement de {firstObject.gameObject.name} vers {newLat:F6}, {newLon:F6}");
            firstObject.UpdateGPSCoordinates(newLat, newLon);
        }

        /// <summary>
        /// Affiche des informations sur tous les objets GPS
        /// </summary>
        [ContextMenu("Afficher infos objets GPS")]
        public void ShowGPSObjectsInfo()
        {
            var gpsComponents = FindObjectsByType<GPSPlaceableComponent>(FindObjectsSortMode.None);
            
            Debug.Log($"=== INFORMATIONS OBJETS GPS ({gpsComponents.Length} objets) ===");
            
            foreach (var gpsObj in gpsComponents)
            {
                string status = gpsObj.IsValid ? "✅ VALIDE" : "❌ INVALIDE";
                string placed = gpsObj.IsPlaced ? "PLACÉ" : "NON PLACÉ";
                
                Debug.Log($"{gpsObj.gameObject.name}: {status} | {placed} | GPS: {gpsObj.GPSCoordinates}");
            }

            // Informations du GPSObjectPlacer si disponible
            if (gpsObjectPlacer != null)
            {
                var validObjects = gpsObjectPlacer.GetValidObjects();
                var invalidObjects = gpsObjectPlacer.GetInvalidObjects();
                
                Debug.Log($"GPSObjectPlacer: {validObjects.Count} objets valides, {invalidObjects.Count} objets invalides");
            }
        }

        /// <summary>
        /// Calcule les distances entre objets GPS
        /// </summary>
        [ContextMenu("Calculer distances GPS")]
        public void CalculateGPSDistances()
        {
            var gpsComponents = FindObjectsByType<GPSPlaceableComponent>(FindObjectsSortMode.None);
            
            if (gpsComponents.Length < 2)
            {
                Debug.LogWarning("Il faut au moins 2 objets GPS pour calculer des distances !");
                return;
            }

            Debug.Log("=== DISTANCES ENTRE OBJETS GPS ===");
            
            for (int i = 0; i < gpsComponents.Length; i++)
            {
                for (int j = i + 1; j < gpsComponents.Length; j++)
                {
                    var obj1 = gpsComponents[i];
                    var obj2 = gpsComponents[j];
                    
                    if (obj1.IsValid && obj2.IsValid)
                    {
                        float distance = obj1.GetDistanceTo(obj2);
                        Debug.Log($"{obj1.gameObject.name} ↔ {obj2.gameObject.name}: {distance:F1}m");
                    }
                }
            }
        }
    }
}
