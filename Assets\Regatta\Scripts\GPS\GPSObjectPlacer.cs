using System.Collections.Generic;
using UnityEngine;
using Mapbox.Unity.Map;
using Mapbox.Utils;

namespace Regatta.GPS
{
    /// <summary>
    /// Système pour placer automatiquement des objets de décor basés sur leurs coordonnées GPS
    /// Les objets ne sont visibles que si leurs coordonnées sont valides par rapport à la carte
    /// </summary>
    public class GPSObjectPlacer : MonoBehaviour
    {
        [Header("Configuration")]
        [SerializeField] private AbstractMap map;
        [SerializeField] private bool autoFindMap = true;
        [SerializeField] private float defaultHeightOffset = 1f;
        [SerializeField] private bool hideInvalidObjects = true;
        [SerializeField] private bool logPlacementDetails = true;

        [Header("Objets GPS à placer")]
        [SerializeField] private List<GPSPlaceableObject> gpsObjects = new List<GPSPlaceableObject>();

        private GPSPositionConverter gpsConverter;
        private bool isMapInitialized = false;

        /// <summary>
        /// Données d'un objet à placer via GPS
        /// </summary>
        [System.Serializable]
        public class GPSPlaceableObject
        {
            [Header("Objet")]
            public GameObject gameObject;
            public string objectName = "Objet GPS";
            
            [Header("Coordonnées GPS")]
            [Tooltip("Latitude (ex: 48.8566 pour Paris)")]
            public double latitude;
            [Tooltip("Longitude (ex: 2.3522 pour Paris)")]
            public double longitude;
            
            [Header("Placement")]
            [Tooltip("Hauteur Y personnalisée (laissez 0 pour utiliser la hauteur par défaut)")]
            public float customHeight = 0f;
            [Tooltip("Rotation Y à appliquer à l'objet")]
            public float rotationY = 0f;
            
            [Header("Validation")]
            [Tooltip("Si coché, l'objet sera caché si les coordonnées sont invalides")]
            public bool hideIfInvalid = true;
            
            // État interne
            [System.NonSerialized]
            public bool isPlaced = false;
            [System.NonSerialized]
            public bool isValid = false;
            [System.NonSerialized]
            public Vector3 worldPosition;

            public Vector2d GPSCoordinates => new Vector2d(latitude, longitude);
            
            public bool IsConfigured => gameObject != null && latitude != 0 && longitude != 0;
        }

        private void Start()
        {
            InitializeMap();
        }

        private void InitializeMap()
        {
            // Trouver automatiquement la carte si nécessaire
            if (autoFindMap && map == null)
            {
                map = FindFirstObjectByType<AbstractMap>();
            }

            if (map == null)
            {
                Debug.LogError("[GPSObjectPlacer] Aucune carte Mapbox trouvée ! Assignez une AbstractMap ou activez autoFindMap.");
                return;
            }

            // Créer le convertisseur GPS
            gpsConverter = new GPSPositionConverter(map, defaultHeightOffset);

            // Vérifier si la carte est déjà initialisée
            if (map.CenterLatitudeLongitude != default(Vector2d))
            {
                OnMapInitialized();
            }
            else
            {
                // S'abonner à l'événement d'initialisation
                map.OnInitialized += OnMapInitialized;
            }
        }

        private void OnMapInitialized()
        {
            isMapInitialized = true;
            map.OnInitialized -= OnMapInitialized;
            
            if (logPlacementDetails)
            {
                Debug.Log($"[GPSObjectPlacer] Carte initialisée. Placement de {gpsObjects.Count} objets GPS...");
            }

            PlaceAllObjects();
        }

        /// <summary>
        /// Place tous les objets GPS configurés
        /// </summary>
        public void PlaceAllObjects()
        {
            if (!isMapInitialized)
            {
                Debug.LogWarning("[GPSObjectPlacer] Tentative de placement alors que la carte n'est pas initialisée.");
                return;
            }

            int placedCount = 0;
            int validCount = 0;

            foreach (var gpsObj in gpsObjects)
            {
                if (PlaceObject(gpsObj))
                {
                    placedCount++;
                    if (gpsObj.isValid) validCount++;
                }
            }

            if (logPlacementDetails)
            {
                Debug.Log($"[GPSObjectPlacer] Placement terminé: {placedCount} objets traités, {validCount} valides et placés.");
            }
        }

        /// <summary>
        /// Place un objet GPS spécifique
        /// </summary>
        /// <param name="gpsObj">L'objet à placer</param>
        /// <returns>True si l'objet a été traité (même s'il est invalide)</returns>
        public bool PlaceObject(GPSPlaceableObject gpsObj)
        {
            if (!gpsObj.IsConfigured)
            {
                Debug.LogWarning($"[GPSObjectPlacer] Objet '{gpsObj.objectName}' mal configuré (GameObject ou coordonnées manquantes).");
                return false;
            }

            // Valider les coordonnées GPS
            Vector2d gpsCoords = gpsObj.GPSCoordinates;
            gpsObj.isValid = GPSPositionConverter.IsValidGPSCoordinate(gpsCoords);

            if (!gpsObj.isValid)
            {
                if (logPlacementDetails)
                {
                    Debug.LogWarning($"[GPSObjectPlacer] Coordonnées GPS invalides pour '{gpsObj.objectName}': {gpsCoords}");
                }

                // Cacher l'objet si demandé
                if (gpsObj.hideIfInvalid || hideInvalidObjects)
                {
                    gpsObj.gameObject.SetActive(false);
                }
                return true;
            }

            // Convertir les coordonnées GPS en position Unity
            float heightY = gpsObj.customHeight > 0 ? gpsObj.customHeight : defaultHeightOffset;
            Vector3 worldPos = gpsConverter.GPSToUnityPosition(gpsCoords, heightY);

            if (worldPos == Vector3.zero)
            {
                Debug.LogWarning($"[GPSObjectPlacer] Échec de conversion GPS→Unity pour '{gpsObj.objectName}'");
                gpsObj.isValid = false;
                if (gpsObj.hideIfInvalid || hideInvalidObjects)
                {
                    gpsObj.gameObject.SetActive(false);
                }
                return true;
            }

            // Placer l'objet
            gpsObj.worldPosition = worldPos;
            gpsObj.gameObject.transform.position = worldPos;
            
            // Appliquer la rotation si spécifiée
            if (gpsObj.rotationY != 0)
            {
                Vector3 rotation = gpsObj.gameObject.transform.eulerAngles;
                rotation.y = gpsObj.rotationY;
                gpsObj.gameObject.transform.eulerAngles = rotation;
            }

            // S'assurer que l'objet est visible
            gpsObj.gameObject.SetActive(true);
            gpsObj.isPlaced = true;

            if (logPlacementDetails)
            {
                Debug.Log($"[GPSObjectPlacer] '{gpsObj.objectName}' placé à {gpsCoords} → {worldPos}");
            }

            return true;
        }

        /// <summary>
        /// Ajoute un nouvel objet GPS à placer
        /// </summary>
        /// <param name="obj">GameObject à placer</param>
        /// <param name="latitude">Latitude GPS</param>
        /// <param name="longitude">Longitude GPS</param>
        /// <param name="name">Nom descriptif (optionnel)</param>
        /// <returns>L'objet GPS créé</returns>
        public GPSPlaceableObject AddGPSObject(GameObject obj, double latitude, double longitude, string name = null)
        {
            var gpsObj = new GPSPlaceableObject
            {
                gameObject = obj,
                objectName = name ?? obj.name,
                latitude = latitude,
                longitude = longitude
            };

            gpsObjects.Add(gpsObj);

            // Placer immédiatement si la carte est prête
            if (isMapInitialized)
            {
                PlaceObject(gpsObj);
            }

            return gpsObj;
        }

        /// <summary>
        /// Supprime un objet GPS de la liste
        /// </summary>
        public void RemoveGPSObject(GPSPlaceableObject gpsObj)
        {
            gpsObjects.Remove(gpsObj);
        }

        /// <summary>
        /// Remet à jour le placement de tous les objets (utile après changement de carte)
        /// </summary>
        public void RefreshAllPlacements()
        {
            if (isMapInitialized)
            {
                PlaceAllObjects();
            }
        }

        /// <summary>
        /// Obtient la liste des objets GPS valides et placés
        /// </summary>
        public List<GPSPlaceableObject> GetValidObjects()
        {
            return gpsObjects.FindAll(obj => obj.isValid && obj.isPlaced);
        }

        /// <summary>
        /// Obtient la liste des objets GPS invalides
        /// </summary>
        public List<GPSPlaceableObject> GetInvalidObjects()
        {
            return gpsObjects.FindAll(obj => !obj.isValid);
        }

        private void OnDestroy()
        {
            if (map != null)
            {
                map.OnInitialized -= OnMapInitialized;
            }
        }

        // Méthodes pour l'éditeur Unity
        #if UNITY_EDITOR
        [ContextMenu("Placer tous les objets")]
        private void EditorPlaceAllObjects()
        {
            if (Application.isPlaying)
            {
                PlaceAllObjects();
            }
            else
            {
                Debug.LogWarning("[GPSObjectPlacer] Le placement ne fonctionne qu'en mode Play.");
            }
        }

        [ContextMenu("Valider toutes les coordonnées")]
        private void EditorValidateCoordinates()
        {
            int validCount = 0;
            foreach (var gpsObj in gpsObjects)
            {
                if (gpsObj.IsConfigured && GPSPositionConverter.IsValidGPSCoordinate(gpsObj.GPSCoordinates))
                {
                    validCount++;
                }
            }
            Debug.Log($"[GPSObjectPlacer] {validCount}/{gpsObjects.Count} objets ont des coordonnées valides.");
        }
        #endif
    }
}
