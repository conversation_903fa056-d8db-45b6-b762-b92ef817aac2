using UnityEngine;
using Mapbox.Utils;

namespace Regatta.GPS
{
    /// <summary>
    /// Composant à attacher directement sur un GameObject pour le placer automatiquement via GPS
    /// Alternative plus simple au GPSObjectPlacer pour des objets individuels
    /// </summary>
    public class GPSPlaceableComponent : MonoBehaviour
    {
        [Header("Coordonnées GPS")]
        [Tooltip("Latitude (ex: 48.8566 pour Paris)")]
        public double latitude;
        [Tooltip("Longitude (ex: 2.3522 pour Paris)")]
        public double longitude;
        
        [Header("Configuration")]
        [Tooltip("Hauteur Y personnalisée (0 = utiliser la hauteur par défaut)")]
        public float customHeight = 0f;
        [Tooltip("Rotation Y à appliquer")]
        public float rotationY = 0f;
        [Tooltip("Cacher l'objet si les coordonnées sont invalides")]
        public bool hideIfInvalid = true;
        [Tooltip("Placer automatiquement au démarrage")]
        public bool autoPlace = true;
        [Tooltip("Afficher les logs de placement")]
        public bool showLogs = true;

        [<PERSON><PERSON>("Informations (lecture seule)")]
        [SerializeField, Tooltip("Coordonnées valides ?")]
        private bool isValid = false;
        [SerializeField, Tooltip("Objet placé ?")]
        private bool isPlaced = false;
        [SerializeField, Tooltip("Position Unity calculée")]
        private Vector3 calculatedPosition;

        private GPSObjectPlacer placer;
        private GPSObjectPlacer.GPSPlaceableObject gpsObjectData;

        public bool IsValid => isValid;
        public bool IsPlaced => isPlaced;
        public Vector2d GPSCoordinates => new Vector2d(latitude, longitude);

        private void Start()
        {
            if (autoPlace)
            {
                PlaceObject();
            }
        }

        /// <summary>
        /// Place l'objet selon ses coordonnées GPS
        /// </summary>
        public void PlaceObject()
        {
            // Trouver ou créer un GPSObjectPlacer
            if (placer == null)
            {
                placer = FindFirstObjectByType<GPSObjectPlacer>();
                
                if (placer == null)
                {
                    // Créer un GPSObjectPlacer temporaire
                    GameObject placerObj = new GameObject("GPSObjectPlacer (Auto-created)");
                    placer = placerObj.AddComponent<GPSObjectPlacer>();
                    
                    if (showLogs)
                    {
                        Debug.Log($"[GPSPlaceableComponent] GPSObjectPlacer créé automatiquement pour '{gameObject.name}'");
                    }
                }
            }

            // Créer les données GPS pour cet objet
            if (gpsObjectData == null)
            {
                gpsObjectData = new GPSObjectPlacer.GPSPlaceableObject
                {
                    gameObject = this.gameObject,
                    objectName = this.gameObject.name,
                    latitude = this.latitude,
                    longitude = this.longitude,
                    customHeight = this.customHeight,
                    rotationY = this.rotationY,
                    hideIfInvalid = this.hideIfInvalid
                };
            }
            else
            {
                // Mettre à jour les données si elles ont changé
                gpsObjectData.latitude = this.latitude;
                gpsObjectData.longitude = this.longitude;
                gpsObjectData.customHeight = this.customHeight;
                gpsObjectData.rotationY = this.rotationY;
                gpsObjectData.hideIfInvalid = this.hideIfInvalid;
            }

            // Placer l'objet
            bool success = placer.PlaceObject(gpsObjectData);
            
            if (success)
            {
                isValid = gpsObjectData.isValid;
                isPlaced = gpsObjectData.isPlaced;
                calculatedPosition = gpsObjectData.worldPosition;

                if (showLogs)
                {
                    if (isValid && isPlaced)
                    {
                        Debug.Log($"[GPSPlaceableComponent] '{gameObject.name}' placé avec succès à {GPSCoordinates} → {calculatedPosition}");
                    }
                    else if (!isValid)
                    {
                        Debug.LogWarning($"[GPSPlaceableComponent] '{gameObject.name}' a des coordonnées GPS invalides: {GPSCoordinates}");
                    }
                }
            }
            else
            {
                if (showLogs)
                {
                    Debug.LogError($"[GPSPlaceableComponent] Échec du placement de '{gameObject.name}'");
                }
            }
        }

        /// <summary>
        /// Met à jour les coordonnées GPS et replace l'objet
        /// </summary>
        /// <param name="newLatitude">Nouvelle latitude</param>
        /// <param name="newLongitude">Nouvelle longitude</param>
        public void UpdateGPSCoordinates(double newLatitude, double newLongitude)
        {
            latitude = newLatitude;
            longitude = newLongitude;
            PlaceObject();
        }

        /// <summary>
        /// Valide les coordonnées GPS actuelles
        /// </summary>
        /// <returns>True si les coordonnées sont valides</returns>
        public bool ValidateCoordinates()
        {
            return GPSPositionConverter.IsValidGPSCoordinate(GPSCoordinates);
        }

        /// <summary>
        /// Obtient la distance en mètres par rapport à d'autres coordonnées GPS
        /// </summary>
        /// <param name="otherLatitude">Latitude de l'autre point</param>
        /// <param name="otherLongitude">Longitude de l'autre point</param>
        /// <returns>Distance en mètres</returns>
        public float GetDistanceToGPS(double otherLatitude, double otherLongitude)
        {
            return GPSPositionConverter.CalculateDistance(GPSCoordinates, new Vector2d(otherLatitude, otherLongitude));
        }

        /// <summary>
        /// Obtient la distance en mètres par rapport à un autre objet GPS
        /// </summary>
        /// <param name="other">Autre composant GPS</param>
        /// <returns>Distance en mètres</returns>
        public float GetDistanceTo(GPSPlaceableComponent other)
        {
            return GetDistanceToGPS(other.latitude, other.longitude);
        }

        // Méthodes pour l'éditeur Unity
        #if UNITY_EDITOR
        [ContextMenu("Placer l'objet")]
        private void EditorPlaceObject()
        {
            if (Application.isPlaying)
            {
                PlaceObject();
            }
            else
            {
                Debug.LogWarning("[GPSPlaceableComponent] Le placement ne fonctionne qu'en mode Play.");
            }
        }

        [ContextMenu("Valider les coordonnées")]
        private void EditorValidateCoordinates()
        {
            bool valid = ValidateCoordinates();
            if (valid)
            {
                Debug.Log($"[GPSPlaceableComponent] Coordonnées valides pour '{gameObject.name}': {GPSCoordinates}");
            }
            else
            {
                Debug.LogWarning($"[GPSPlaceableComponent] Coordonnées INVALIDES pour '{gameObject.name}': {GPSCoordinates}");
            }
        }

        [ContextMenu("Copier les coordonnées")]
        private void EditorCopyCoordinates()
        {
            string coords = $"{latitude:F6},{longitude:F6}";
            GUIUtility.systemCopyBuffer = coords;
            Debug.Log($"[GPSPlaceableComponent] Coordonnées copiées: {coords}");
        }

        // Validation dans l'inspecteur
        private void OnValidate()
        {
            // Limiter les coordonnées dans des plages raisonnables
            latitude = System.Math.Max(-90, System.Math.Min(90, latitude));
            longitude = System.Math.Max(-180, System.Math.Min(180, longitude));
            
            // Mettre à jour les informations de validation
            if (Application.isPlaying)
            {
                isValid = ValidateCoordinates();
            }
        }

        // Affichage dans la scène
        private void OnDrawGizmosSelected()
        {
            if (ValidateCoordinates())
            {
                Gizmos.color = Color.green;
                Gizmos.DrawWireSphere(transform.position, 2f);
                
                // Afficher les coordonnées
                #if UNITY_EDITOR
                UnityEditor.Handles.Label(transform.position + Vector3.up * 3f, 
                    $"GPS: {latitude:F6}, {longitude:F6}");
                #endif
            }
            else
            {
                Gizmos.color = Color.red;
                Gizmos.DrawWireCube(transform.position, Vector3.one * 2f);
                
                #if UNITY_EDITOR
                UnityEditor.Handles.Label(transform.position + Vector3.up * 3f, 
                    "GPS: INVALIDE");
                #endif
            }
        }
        #endif
    }
}
